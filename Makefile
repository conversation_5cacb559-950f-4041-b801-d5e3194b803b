# arm-none-eabi-gcc-9.2.1
GCCPATH			?= F:\Tools\Compilers\gcc-arm-none-eabi-9-2019-q4-major-win32

CROSS_COMPILE 	?= $(GCCPATH)/bin/arm-none-eabi-

TARGET		  	?= soc2018_freertos

CC 				:= $(CROSS_COMPILE)gcc
CXX				:= $(CROSS_COMPILE)g++
LD				:= $(CROSS_COMPILE)gcc
OBJCOPY 		:= $(CROSS_COMPILE)objcopy
OBJDUMP 		:= $(CROSS_COMPILE)objdump

# 这里的路径需要根据自己的路径设置
# arm-none-eabi-gcc-9.2.1
LIBPATH			:= -lgcc -L $(GCCPATH)/lib/gcc/arm-none-eabi/9.2.1 \
				   -L $(GCCPATH)/arm-none-eabi/lib

INCDIRS 		:=
# 用户头文件，系统基础头文件
INCDIRS 		+= core/Inc \
                   imx6ul
# 驱动头文件
INCDIRS 		+= drivers/inc
# FreeRTOS移植头文件
INCDIRS 		+= freertos \
				   freertos/include \
				   freertos/portable/GCC/ARM_CA9 \
				   freertos/portable/MemMang

SRCDIRS			:=
# 用户源文件，系统基础源文件
SRCDIRS 		+= core \
				   core/src \
                   imx6ul
# 驱动源文件
SRCDIRS 		+= drivers/src
# FreeRTOS源文件
SRCDIRS 		+= freertos \
				   freertos/include \
				   freertos/portable/GCC/ARM_CA9 \
				   freertos/portable/MemMang				   
				   
INCLUDE			:= $(patsubst %, -I %, $(INCDIRS))

SFILES			:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.S))
CFILES			:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CPPFILES		:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))

SFILENDIR		:= $(notdir  $(SFILES))
CFILENDIR		:= $(notdir  $(CFILES))
CPPFILENDIR		:= $(notdir  $(CPPFILES))

SOBJS			:= $(patsubst %, obj/%, $(SFILENDIR:.S=.o))
COBJS			:= $(patsubst %, obj/%, $(CFILENDIR:.c=.o))
CPPOBJS			:= $(patsubst %, obj/%, $(CPPFILENDIR:.cpp=.o))
OBJS			:= $(SOBJS) $(COBJS) $(CPPOBJS)

VPATH			:= $(SRCDIRS)

# 创建obj目录
$(shell mkdir -p obj)

.PHONY: clean
# link
$(TARGET).bin : $(OBJS)
	$(LD) -Timx6ul.lds -o $(TARGET).elf $^ $(LIBPATH) -marm -mcpu=cortex-a7 -mtune=cortex-a7 -mfpu=neon-vfpv4  -mfloat-abi=softfp --specs=nosys.specs -lnosys -lstdc++ -lc -lm
	$(OBJCOPY) -O binary -S $(TARGET).elf $@
	$(OBJDUMP) -D -m arm $(TARGET).elf > $(TARGET).dis

# assembly
$(SOBJS) : obj/%.o : %.S
	$(CC) -Wall -marm  -mtune=cortex-a7 -mcpu=cortex-a7 -mthumb -mfpu=neon-vfpv4  -mfloat-abi=softfp -c -O0  $(INCLUDE) -o $@ $<

# c compile
$(COBJS) : obj/%.o : %.c
	$(CC) -Wall -marm  -mtune=cortex-a7 -mcpu=cortex-a7 -Wa,-mthumb -mfpu=neon-vfpv4 -mfloat-abi=softfp -c -O0  $(INCLUDE) -o $@ $< 
	
# cpp compile
$(CPPOBJS) : obj/%.o : %.cpp
	$(CXX) -Wall -marm  -mtune=cortex-a7 -mcpu=cortex-a7 -Wa,-mthumb -mfpu=neon-vfpv4 -mfloat-abi=softfp -c -O0 $(INCLUDE) -o $@ $<

clean:
	rm -rf $(TARGET).elf $(TARGET).dis $(TARGET).bin load.imx obj
