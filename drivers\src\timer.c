
#include "soc502_parameter.h"
#include "types.h"
#include "irq_gic.h"


/*
private timer and watchdog register:
00 load
04 counter
08 control
0C interrupt status

30 wathcdog reset status
34 wathcdog disable 
*/
/*
global timer :
00 lower load
04 upper load
08 control
0C interrupt status

10/14 compator
18 increment
*/

u8 rlt;
//private timer中断处理函数
void private_timer_handle_irq(void)
{
	unsigned int irq,intStatus = 0;

//	print2("\r\ngic: private timer handle irq \r\n");
	//intStatus = r32(A9_PRIVATE_BASE+0x04);
//	print2("\r\nprivate timer irq Status = 0x%x\r\n",r32(A9_PRIVATE_BASE+0x0C));
//	print2("\r\nprivate timer counter read data is %x -\r\n",intStatus);

	rlt &= 0x1;

	w32(A9_PRIVATE_BASE + 0x0C,1);
}
//global timer中断处理函数
void global_timer_handle_irq(void)
{
	unsigned int irq,intStatus = 0;

//	intStatus = r32(A9_GLOBAL_TIMER_BASE+0x00);	
//	print2("\r\ngic: global timer handle irq \r\n");
//
//	print2("\r\nglobal timer lower counter read data is %x -\r\n",intStatus);
//	intStatus = r32(A9_GLOBAL_TIMER_BASE+0x04);	
//	print2("\r\nglobal timer upper counter read data is %x -\r\n",intStatus);
//	print2("\r\nglobal timer irq Status = 0x%x\r\n",r32(A9_GLOBAL_TIMER_BASE+0x0C));

	w32(A9_GLOBAL_TIMER_BASE + 0x00,0x00000000);
	w32(A9_GLOBAL_TIMER_BASE + 0x04,0x00000000);
//	w32(A9_GLOBAL_TIMER_BASE + 0x10,0x3B9ACA00);
//	w32(A9_GLOBAL_TIMER_BASE + 0x14,0x00000000);
//	w32(A9_GLOBAL_TIMER_BASE + 0x0C,0x00000001);

	rlt &= 0x2;

	while(r32(A9_GLOBAL_TIMER_BASE + 0x0c))
	{
		w32(A9_GLOBAL_TIMER_BASE + 0x0C,0x00000001);
		w32(A9_GLOBAL_TIMER_BASE + 0x0C,0x00000001);
	}
	
}

//////////////////////////////////////////////
// basic functions
/////////////////////////////////////////////
u32 timer_A9_test()
{
	u32 counter;
	u32 reset;
	u32 lower,upper;	


 	//int cmd;
	gic_set_type(29, IRQ_TYPE_EDGE_RISING);

	gic_register_irq_entry(29, private_timer_handle_irq);
	gic_enable_irq(29);
	
	//private timer test--reload
//	print2("\r\n\r\n------private timer test--auto load------\r\n");

	rlt = 0x3;//bit1:privatetimer err flag  bit0:globaltimer err flag
	
	w32(A9_PRIVATE_BASE + 0x08, 0x00);
//	w32(A9_PRIVATE_BASE + 0x00, 0x1DCD6500);		//write to load register  500MHz,1s
//	w32(A9_PRIVATE_BASE + 0x04, 0x1DCD6500);		//write to load register  500MHz,1s
	w32(A9_PRIVATE_BASE + 0x00, 0x2FAF080);		//write to load register  500MHz,100ms
	w32(A9_PRIVATE_BASE + 0x04, 0x2FAF080);		//write to load register  
	w32(A9_PRIVATE_BASE + 0x08, 0x07);		//control register:enbale interrput/auto load/enbale timer 
	delay_ms(100);
//	counter = r32(A9_PRIVATE_BASE + 0x04);		//read counter register
//	print2("\r\ntimer counter read data is %x -\r\n",counter);


	w32(A9_PRIVATE_BASE + 0x08, 0x00);



	//global timer:auto increment, step=0
//	print2("\r\n\r\n------global timer test--auto increment, step=0------\r\n");

	gic_set_type(27, IRQ_TYPE_EDGE_RISING);
//	gic_set_type(27, IRQ_TYPE_LEVEL_HIGH);
	gic_register_irq_entry(27, global_timer_handle_irq);
	gic_enable_irq(27);

	w32(A9_GLOBAL_TIMER_BASE + 0x08,0x00);				//write control reg:00
	w32(A9_GLOBAL_TIMER_BASE + 0x00,0x0);		//write load register
	w32(A9_GLOBAL_TIMER_BASE + 0x04,0x00000000);
//	w32(A9_GLOBAL_TIMER_BASE + 0x10,0x1DCD6500);		//write compare register:500MHz,1s
	w32(A9_GLOBAL_TIMER_BASE + 0x10,0x2FAF080);		//write compare register:500MHz,100ms
	w32(A9_GLOBAL_TIMER_BASE + 0x14,0x000000000);
	w32(A9_GLOBAL_TIMER_BASE + 0x18,0x000000000);
	w32(A9_GLOBAL_TIMER_BASE + 0x0c,0x00000001);
	w32(A9_GLOBAL_TIMER_BASE + 0x08,0x0F);		//auto increment/enable interrupt/enbale compare/enbale global timer

	SYS_Delay(150000);
	w32(A9_GLOBAL_TIMER_BASE + 0x08,0x00);	

	return rlt;
}



